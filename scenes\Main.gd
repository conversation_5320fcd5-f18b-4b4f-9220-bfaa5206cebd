extends Node2D
class_name Main

# 主场景
# 整合所有系统，管理游戏流程

@onready var map: GameMap = $Map
@onready var hud: HUD = $UI/HUD
@onready var camera: Camera2D = $Camera2D

# 系统节点
@onready var production_system: ProductionSystem = $Systems/ProductionSystem
@onready var economy_system: EconomySystem = $Systems/EconomySystem
@onready var victory_system: VictorySystem = $Systems/VictorySystem

var player_force_id: int = -1
var ai_force_id: int = -1

func _ready():
	print("Main scene initialized")
	
	# 连接游戏信号
	Game.game_started.connect(_on_game_started)
	Game.game_ended.connect(_on_game_ended)
	
	# 连接胜利系统信号
	if victory_system:
		victory_system.victory_achieved.connect(_on_victory_achieved)
	
	# 设置摄像机
	setup_camera()
	
	# 开始游戏
	start_game()

func setup_camera():
	"""设置摄像机"""
	if camera and map:
		# 将摄像机定位到地图中心
		var map_center = Vector2(
			map.map_size.x * Config.CELL_SIZE / 2,
			map.map_size.y * Config.CELL_SIZE / 2
		)
		camera.position = map_center
		camera.zoom = Vector2(0.5, 0.5)  # 缩小以看到更多区域

func start_game():
	"""开始游戏"""
	print("Starting game...")
	
	# 设置地图
	map.setup(Vector2i(100, 100))
	
	# 注册势力
	register_forces()
	
	# 设置胜利条件
	victory_system.set_victory_type("conquest")
	
	# 生成核心
	spawn_cores()
	
	# 初始化 HUD
	hud.initialize(player_force_id)
	
	# 开始游戏
	Game.start_match({
		"map_size": Vector2i(100, 100),
		"initial_energy": 1000,
		"victory_mode": "conquest"
	})

func register_forces():
	"""注册势力"""
	# 注册玩家势力
	player_force_id = Game.register_force({
		"color": Config.get_force_color(0),  # 蓝色
		"is_ai": false
	})
	
	# 注册AI势力
	ai_force_id = Game.register_force({
		"color": Config.get_force_color(1),  # 红色
		"is_ai": true
	})
	
	print("Registered player force: ", player_force_id)
	print("Registered AI force: ", ai_force_id)

func spawn_cores():
	"""生成核心"""
	var core_scene = preload("res://scenes/Entities/Core.tscn")
	
	# 生成玩家核心
	var player_core = core_scene.instantiate()
	var player_pos = map.get_random_corner_position()
	player_core.initialize(player_force_id, player_pos)
	map.add_entity(player_core)
	
	# 生成AI核心
	var ai_core = core_scene.instantiate()
	var ai_pos = map.get_random_corner_position()
	# 确保AI核心不会太靠近玩家核心
	while ai_pos.distance_to(player_pos) < 500:
		ai_pos = map.get_random_corner_position()
	
	ai_core.initialize(ai_force_id, ai_pos)
	map.add_entity(ai_core)
	
	print("Spawned cores at: Player=", player_pos, " AI=", ai_pos)

func _on_game_started():
	"""游戏开始"""
	print("Game started!")
	
	# 启动简单的AI
	start_simple_ai()

func _on_game_ended(winner_team_id: int):
	"""游戏结束"""
	print("Game ended! Winner team: ", winner_team_id)
	
	# 显示胜利消息
	if hud:
		hud.show_victory_message(winner_team_id, "conquest")

func _on_victory_achieved(winner_team_id: int, victory_type: String):
	"""胜利达成"""
	print("Victory achieved by team ", winner_team_id, " via ", victory_type)

func start_simple_ai():
	"""启动简单AI"""
	# 创建一个简单的AI定时器
	var ai_timer = Timer.new()
	ai_timer.wait_time = Config.AI_PRODUCTION_INTERVAL
	ai_timer.timeout.connect(_on_ai_tick)
	ai_timer.autostart = true
	add_child(ai_timer)

func _on_ai_tick():
	"""AI tick"""
	if Game.current_state != Game.GameState.PLAYING:
		return
	
	# 简单AI逻辑：随机生产单位
	if Game.production_ref and Game.economy_ref:
		var blueprints = Game.production_ref.get_all_default_blueprints()
		if not blueprints.is_empty():
			var random_blueprint = blueprints[randi() % blueprints.size()]
			
			# 检查是否能生产
			if Game.production_ref.can_produce(ai_force_id, random_blueprint):
				Game.production_ref.request_production(ai_force_id, random_blueprint)
				print("AI produced: ", random_blueprint.unit_name)

# 输入处理
func _input(event):
	"""处理输入"""
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_ESCAPE:
				# 暂停或退出
				print("ESC pressed")
			KEY_SPACE:
				# 暂停/继续
				print("SPACE pressed")
			KEY_1, KEY_2, KEY_3, KEY_4, KEY_5:
				# 快捷键生产
				var index = event.keycode - KEY_1
				request_production_by_index(index)

func request_production_by_index(index: int):
	"""通过索引请求生产"""
	if Game.production_ref:
		var blueprints = Game.production_ref.get_all_default_blueprints()
		if index >= 0 and index < blueprints.size():
			Game.production_ref.request_production(player_force_id, blueprints[index])

# 摄像机控制
func _process(delta):
	"""每帧更新"""
	handle_camera_movement(delta)

func handle_camera_movement(delta: float):
	"""处理摄像机移动"""
	if not camera:
		return
	
	var move_speed = 300.0 * delta / camera.zoom.x  # 根据缩放调整速度
	var movement = Vector2.ZERO
	
	if Input.is_action_pressed("ui_left") or Input.is_key_pressed(KEY_A):
		movement.x -= move_speed
	if Input.is_action_pressed("ui_right") or Input.is_key_pressed(KEY_D):
		movement.x += move_speed
	if Input.is_action_pressed("ui_up") or Input.is_key_pressed(KEY_W):
		movement.y -= move_speed
	if Input.is_action_pressed("ui_down") or Input.is_key_pressed(KEY_S):
		movement.y += move_speed
	
	camera.position += movement
	
	# 限制摄像机在地图范围内
	var map_bounds = Rect2(
		Vector2.ZERO,
		Vector2(map.map_size.x * Config.CELL_SIZE, map.map_size.y * Config.CELL_SIZE)
	)
	
	camera.position.x = clamp(camera.position.x, map_bounds.position.x, map_bounds.end.x)
	camera.position.y = clamp(camera.position.y, map_bounds.position.y, map_bounds.end.y)

func _unhandled_input(event):
	"""处理未处理的输入"""
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			# 放大
			camera.zoom *= 1.1
			camera.zoom = camera.zoom.clamp(Vector2(0.1, 0.1), Vector2(2.0, 2.0))
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			# 缩小
			camera.zoom *= 0.9
			camera.zoom = camera.zoom.clamp(Vector2(0.1, 0.1), Vector2(2.0, 2.0))
