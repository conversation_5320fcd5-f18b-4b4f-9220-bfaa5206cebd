extends Node
class_name EconomySystem

# 经济系统
# 管理能量收入、支出、赏金等经济活动

signal energy_changed(force_id: int, new_amount: int)
signal energy_gained(force_id: int, amount: int, source: String)
signal energy_spent(force_id: int, amount: int, purpose: String)

func _ready():
	# 设置系统引用
	Game.economy_ref = self
	
	# 连接地图信号
	if Game.map_ref:
		Game.map_ref.energy_tile_occupied.connect(_on_energy_tile_occupied)
		Game.map_ref.energy_tile_freed.connect(_on_energy_tile_freed)
	
	print("Economy system initialized")

func update(delta: float):
	"""经济系统更新"""
	# 处理能源格收入
	process_energy_tile_income(delta)

func process_energy_tile_income(delta: float):
	"""处理能源格收入"""
	if not Game.map_ref:
		return
	
	# 为每个势力计算能源格收入
	for force_id in Game.forces.keys():
		var force = Game.get_force(force_id)
		if not force or force.is_defeated:
			continue
		
		var energy_tiles = Game.map_ref.get_energy_tiles_for_force(force_id)
		var income = energy_tiles.size() * Config.ENERGY_TILE_INCOME_PER_SEC * delta
		
		if income > 0:
			add_energy(force_id, int(income))

func add_energy(force_id: int, amount: int, source: String = ""):
	"""增加能量"""
	var force = Game.get_force(force_id)
	if not force:
		return
	
	force.energy += amount
	energy_changed.emit(force_id, force.energy)
	
	if amount > 0:
		energy_gained.emit(force_id, amount, source)

func spend_energy(force_id: int, amount: int, purpose: String = "") -> bool:
	"""消耗能量"""
	var force = Game.get_force(force_id)
	if not force:
		return false
	
	if force.energy < amount:
		return false
	
	force.energy -= amount
	energy_changed.emit(force_id, force.energy)
	energy_spent.emit(force_id, amount, purpose)
	
	return true

func get_energy(force_id: int) -> int:
	"""获取能量"""
	var force = Game.get_force(force_id)
	return force.energy if force else 0

func can_afford(force_id: int, amount: int) -> bool:
	"""检查是否能负担得起"""
	return get_energy(force_id) >= amount

func grant_bounty(killer_force_id: int, victim_blueprint: Blueprint, victim_level: int):
	"""发放击杀赏金"""
	if victim_level <= 1:
		return  # 1级单位没有赏金
	
	var base_bounty = victim_blueprint.get_price() * Config.BOUNTY_FACTOR
	var level_bounty = base_bounty * victim_level
	var total_bounty = int(level_bounty)
	
	if total_bounty > 0:
		add_energy(killer_force_id, total_bounty, "bounty")
		print("Force ", killer_force_id, " received ", total_bounty, " energy bounty")

func _on_energy_tile_occupied(tile_pos: Vector2i, force_id: int):
	"""能源格被占领"""
	print("Energy tile at ", tile_pos, " occupied by force ", force_id)

func _on_energy_tile_freed(tile_pos: Vector2i):
	"""能源格被释放"""
	print("Energy tile at ", tile_pos, " freed")

# 统计和查询功能
func get_total_energy_income_rate(force_id: int) -> float:
	"""获取总能量收入速率（每秒）"""
	var force = Game.get_force(force_id)
	if not force or force.is_defeated:
		return 0.0
	
	var total_rate = 0.0
	
	# 核心收入
	if force.core_ref and is_instance_valid(force.core_ref):
		total_rate += Config.CORE_INCOME_PER_SEC_PER_LEVEL * force.core_ref.level
	
	# 能源格收入
	if Game.map_ref:
		var energy_tiles = Game.map_ref.get_energy_tiles_for_force(force_id)
		total_rate += energy_tiles.size() * Config.ENERGY_TILE_INCOME_PER_SEC
	
	return total_rate

func get_energy_tile_count(force_id: int) -> int:
	"""获取占领的能源格数量"""
	if not Game.map_ref:
		return 0
	
	return Game.map_ref.get_energy_tiles_for_force(force_id).size()

func get_energy_tile_ratio(force_id: int) -> float:
	"""获取能源格占领比例"""
	if not Game.map_ref:
		return 0.0
	
	var total_tiles = Game.map_ref.get_total_energy_tiles()
	if total_tiles == 0:
		return 0.0
	
	var owned_tiles = get_energy_tile_count(force_id)
	return float(owned_tiles) / float(total_tiles)

func get_team_energy_tile_ratio(team_id: int) -> float:
	"""获取队伍的能源格占领比例"""
	if not Game.map_ref:
		return 0.0
	
	var total_tiles = Game.map_ref.get_total_energy_tiles()
	if total_tiles == 0:
		return 0.0
	
	var team_tiles = 0
	var team = Game.get_team(team_id)
	if team:
		for force_id in team.force_ids:
			team_tiles += get_energy_tile_count(force_id)
	
	return float(team_tiles) / float(total_tiles)

func get_force_economic_info(force_id: int) -> Dictionary:
	"""获取势力经济信息"""
	var force = Game.get_force(force_id)
	if not force:
		return {}
	
	return {
		"energy": force.energy,
		"income_rate": get_total_energy_income_rate(force_id),
		"energy_tiles": get_energy_tile_count(force_id),
		"energy_tile_ratio": get_energy_tile_ratio(force_id),
		"core_level": force.core_ref.level if force.core_ref and is_instance_valid(force.core_ref) else 0
	}

# 调试和管理功能
func set_energy(force_id: int, amount: int):
	"""设置能量（调试用）"""
	var force = Game.get_force(force_id)
	if force:
		force.energy = max(0, amount)
		energy_changed.emit(force_id, force.energy)

func transfer_energy(from_force_id: int, to_force_id: int, amount: int) -> bool:
	"""转移能量（队友间）"""
	var from_force = Game.get_force(from_force_id)
	var to_force = Game.get_force(to_force_id)
	
	if not from_force or not to_force:
		return false
	
	# 检查是否为队友
	if from_force.team_id != to_force.team_id:
		return false
	
	if from_force.energy < amount:
		return false
	
	from_force.energy -= amount
	to_force.energy += amount
	
	energy_changed.emit(from_force_id, from_force.energy)
	energy_changed.emit(to_force_id, to_force.energy)
	
	return true
