extends Resource
class_name Blueprint

# 单位蓝图资源类
# 定义单位的形状、属性、成本等信息

enum Shape {
	CIRCLE,
	TRIANGLE,
	SQUARE,
	PENTAGON,
	HEXAGON
}

@export var shape: Shape = Shape.CIRCLE
@export var unit_name: String = "Unit"
@export var base_stats: Dictionary = {
	"hp": 100.0,
	"attack": 50.0,
	"defense": 10.0,
	"attack_speed": 1.0,
	"attack_range": 3.0,
	"speed": 20.0
}

# 计算得出的属性（运行时计算）
var computed_price: int = 0
var computed_build_time: float = 0.0

func _init():
	# 确保属性在有效范围内
	clamp_stats()
	# 计算成本
	compute_costs()

func clamp_stats():
	"""确保所有属性在有效范围内"""
	base_stats["hp"] = Config.clamp_unit_stat(base_stats.get("hp", 100), "hp")
	base_stats["attack"] = Config.clamp_unit_stat(base_stats.get("attack", 50), "attack")
	base_stats["defense"] = Config.clamp_unit_stat(base_stats.get("defense", 10), "defense")
	base_stats["attack_speed"] = Config.clamp_unit_stat(base_stats.get("attack_speed", 1), "attack_speed")
	base_stats["attack_range"] = Config.clamp_unit_stat(base_stats.get("attack_range", 3), "attack_range")
	base_stats["speed"] = Config.clamp_unit_stat(base_stats.get("speed", 20), "speed")

func compute_costs():
	"""计算单位价格和建造时间"""
	computed_price = Config.calculate_unit_price(base_stats)
	computed_build_time = Config.calculate_build_time(computed_price)

func get_price() -> int:
	"""获取单位价格"""
	if computed_price == 0:
		compute_costs()
	return computed_price

func get_build_time() -> float:
	"""获取建造时间"""
	if computed_build_time == 0.0:
		compute_costs()
	return computed_build_time

func get_shape_name() -> String:
	"""获取形状名称"""
	match shape:
		Shape.CIRCLE:
			return "Circle"
		Shape.TRIANGLE:
			return "Triangle"
		Shape.SQUARE:
			return "Square"
		Shape.PENTAGON:
			return "Pentagon"
		Shape.HEXAGON:
			return "Hexagon"
		_:
			return "Unknown"

func get_stat(stat_name: String) -> float:
	"""获取属性值"""
	return base_stats.get(stat_name, 0.0)

func set_stat(stat_name: String, value: float):
	"""设置属性值"""
	base_stats[stat_name] = Config.clamp_unit_stat(value, stat_name)
	compute_costs()

func duplicate_blueprint() -> Blueprint:
	"""复制蓝图"""
	var new_blueprint = Blueprint.new()
	new_blueprint.shape = shape
	new_blueprint.unit_name = unit_name
	new_blueprint.base_stats = base_stats.duplicate()
	new_blueprint.compute_costs()
	return new_blueprint

# 静态方法：创建默认蓝图
static func create_default_circle() -> Blueprint:
	var blueprint = Blueprint.new()
	blueprint.shape = Shape.CIRCLE
	blueprint.unit_name = "Node"
	blueprint.base_stats = {
		"hp": 80.0,
		"attack": 40.0,
		"defense": 5.0,
		"attack_speed": 1.2,
		"attack_range": 2.0,
		"speed": 25.0
	}
	blueprint.clamp_stats()
	blueprint.compute_costs()
	return blueprint

static func create_default_triangle() -> Blueprint:
	var blueprint = Blueprint.new()
	blueprint.shape = Shape.TRIANGLE
	blueprint.unit_name = "Vanguard"
	blueprint.base_stats = {
		"hp": 60.0,
		"attack": 45.0,
		"defense": 2.0,
		"attack_speed": 1.5,
		"attack_range": 1.5,
		"speed": 35.0
	}
	blueprint.clamp_stats()
	blueprint.compute_costs()
	return blueprint

static func create_default_square() -> Blueprint:
	var blueprint = Blueprint.new()
	blueprint.shape = Shape.SQUARE
	blueprint.unit_name = "Bulwark"
	blueprint.base_stats = {
		"hp": 150.0,
		"attack": 30.0,
		"defense": 20.0,
		"attack_speed": 0.8,
		"attack_range": 2.0,
		"speed": 15.0
	}
	blueprint.clamp_stats()
	blueprint.compute_costs()
	return blueprint

static func create_default_pentagon() -> Blueprint:
	var blueprint = Blueprint.new()
	blueprint.shape = Shape.PENTAGON
	blueprint.unit_name = "Marksman"
	blueprint.base_stats = {
		"hp": 70.0,
		"attack": 60.0,
		"defense": 5.0,
		"attack_speed": 0.7,
		"attack_range": 8.0,
		"speed": 18.0
	}
	blueprint.clamp_stats()
	blueprint.compute_costs()
	return blueprint

static func create_default_hexagon() -> Blueprint:
	var blueprint = Blueprint.new()
	blueprint.shape = Shape.HEXAGON
	blueprint.unit_name = "Coordinator"
	blueprint.base_stats = {
		"hp": 90.0,
		"attack": 35.0,
		"defense": 8.0,
		"attack_speed": 1.0,
		"attack_range": 4.0,
		"speed": 22.0
	}
	blueprint.clamp_stats()
	blueprint.compute_costs()
	return blueprint
