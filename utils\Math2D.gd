extends Node
class_name Math2D

# 2D数学工具类
# 提供几何形状生成、坐标转换等功能

# 生成规则多边形的顶点
static func generate_polygon_points(sides: int, radius: float, rotation: float = 0.0) -> PackedVector2Array:
	"""生成规则多边形顶点"""
	var points = PackedVector2Array()
	
	if sides < 3:
		return points
	
	var angle_step = TAU / sides
	var start_angle = rotation - PI / 2  # 让多边形"直立"
	
	for i in range(sides):
		var angle = start_angle + i * angle_step
		var point = Vector2(cos(angle), sin(angle)) * radius
		points.append(point)
	
	return points

# 生成圆形近似（多边形）
static func generate_circle_points(radius: float, segments: int = 32) -> PackedVector2Array:
	"""生成圆形近似顶点"""
	return generate_polygon_points(segments, radius)

# 根据形状枚举生成顶点
static func generate_shape_points(shape: Blueprint.Shape, radius: float) -> PackedVector2Array:
	"""根据形状生成顶点"""
	match shape:
		Blueprint.Shape.CIRCLE:
			return generate_circle_points(radius, 32)
		Blueprint.Shape.TRIANGLE:
			return generate_polygon_points(3, radius)
		Blueprint.Shape.SQUARE:
			return generate_polygon_points(4, radius)
		Blueprint.Shape.PENTAGON:
			return generate_polygon_points(5, radius)
		Blueprint.Shape.HEXAGON:
			return generate_polygon_points(6, radius)
		_:
			return generate_circle_points(radius, 8)

# 计算多边形面积
static func calculate_polygon_area(points: PackedVector2Array) -> float:
	"""计算多边形面积"""
	if points.size() < 3:
		return 0.0
	
	var area = 0.0
	var n = points.size()
	
	for i in range(n):
		var j = (i + 1) % n
		area += points[i].x * points[j].y
		area -= points[j].x * points[i].y
	
	return abs(area) / 2.0

# 检查点是否在多边形内
static func point_in_polygon(point: Vector2, polygon: PackedVector2Array) -> bool:
	"""检查点是否在多边形内（射线法）"""
	if polygon.size() < 3:
		return false
	
	var inside = false
	var n = polygon.size()
	
	var j = n - 1
	for i in range(n):
		var pi = polygon[i]
		var pj = polygon[j]
		
		if ((pi.y > point.y) != (pj.y > point.y)) and \
		   (point.x < (pj.x - pi.x) * (point.y - pi.y) / (pj.y - pi.y) + pi.x):
			inside = !inside
		
		j = i
	
	return inside

# 计算两点间距离
static func distance(a: Vector2, b: Vector2) -> float:
	"""计算两点间距离"""
	return a.distance_to(b)

# 计算两点间距离的平方（避免开方运算）
static func distance_squared(a: Vector2, b: Vector2) -> float:
	"""计算两点间距离的平方"""
	return a.distance_squared_to(b)

# 向量归一化（安全版本）
static func safe_normalize(vector: Vector2) -> Vector2:
	"""安全的向量归一化"""
	var length = vector.length()
	if length > 0.0001:
		return vector / length
	return Vector2.ZERO

# 角度差计算（考虑环绕）
static func angle_difference(from: float, to: float) -> float:
	"""计算角度差（-PI到PI）"""
	var diff = to - from
	while diff > PI:
		diff -= TAU
	while diff < -PI:
		diff += TAU
	return diff

# 线性插值
static func lerp_vector2(from: Vector2, to: Vector2, weight: float) -> Vector2:
	"""Vector2线性插值"""
	return from.lerp(to, weight)

# 平滑插值
static func smooth_step(from: float, to: float, weight: float) -> float:
	"""平滑插值"""
	weight = clamp(weight, 0.0, 1.0)
	weight = weight * weight * (3.0 - 2.0 * weight)
	return lerp(from, to, weight)

# 获取形状的边界矩形
static func get_shape_bounds(points: PackedVector2Array) -> Rect2:
	"""获取形状的边界矩形"""
	if points.is_empty():
		return Rect2()
	
	var min_x = points[0].x
	var max_x = points[0].x
	var min_y = points[0].y
	var max_y = points[0].y
	
	for point in points:
		min_x = min(min_x, point.x)
		max_x = max(max_x, point.x)
		min_y = min(min_y, point.y)
		max_y = max(max_y, point.y)
	
	return Rect2(min_x, min_y, max_x - min_x, max_y - min_y)

# 计算形状的中心点
static func get_shape_center(points: PackedVector2Array) -> Vector2:
	"""计算形状的中心点"""
	if points.is_empty():
		return Vector2.ZERO
	
	var center = Vector2.ZERO
	for point in points:
		center += point
	
	return center / points.size()

# 旋转点围绕中心
static func rotate_point_around_center(point: Vector2, center: Vector2, angle: float) -> Vector2:
	"""旋转点围绕中心"""
	var cos_a = cos(angle)
	var sin_a = sin(angle)
	var dx = point.x - center.x
	var dy = point.y - center.y
	
	return Vector2(
		center.x + dx * cos_a - dy * sin_a,
		center.y + dx * sin_a + dy * cos_a
	)

# 缩放形状
static func scale_shape(points: PackedVector2Array, scale: float, center: Vector2 = Vector2.ZERO) -> PackedVector2Array:
	"""缩放形状"""
	var scaled_points = PackedVector2Array()
	
	for point in points:
		var offset = point - center
		scaled_points.append(center + offset * scale)
	
	return scaled_points
