extends Node2D
class_name GameMap

# 游戏地图管理器
# 负责网格、能源格、寻路等地图相关功能

signal energy_tile_occupied(tile_pos: Vector2i, force_id: int)
signal energy_tile_freed(tile_pos: Vector2i)

@onready var tile_map: TileMap = $TileMap
@onready var energy_tiles_container: Node2D = $EnergyTiles
@onready var entities_container: Node2D = $Entities

var map_size: Vector2i = Vector2i(100, 100)
var cell_size: int = Config.CELL_SIZE
var astar_grid: AStarGrid2D

# 能源格管理
var energy_tiles: Dictionary = {}  # Vector2i -> EnergyTile
var energy_tile_occupants: Dictionary = {}  # Vector2i -> force_id

func _ready():
	# 设置地图引用
	Game.map_ref = self
	
	# 初始化 AStar 网格
	setup_astar_grid()
	
	print("Map initialized with size: ", map_size)

func setup(size: Vector2i = Vector2i(100, 100)):
	"""设置地图"""
	map_size = size
	
	# 重新设置 AStar 网格
	setup_astar_grid()
	
	# 生成地图内容
	generate_map()

func setup_astar_grid():
	"""设置 AStar 寻路网格"""
	astar_grid = AStarGrid2D.new()
	astar_grid.size = map_size
	astar_grid.cell_size = Vector2(cell_size, cell_size)
	astar_grid.diagonal_mode = AStarGrid2D.DIAGONAL_MODE_ALWAYS
	astar_grid.update()
	
	print("AStar grid setup: ", map_size)

func generate_map():
	"""生成地图内容"""
	# 清理现有内容
	clear_map()
	
	# 生成能源格
	generate_energy_tiles()
	
	print("Map generated with ", energy_tiles.size(), " energy tiles")

func clear_map():
	"""清理地图"""
	# 清理能源格
	for child in energy_tiles_container.get_children():
		child.queue_free()
	energy_tiles.clear()
	energy_tile_occupants.clear()

func generate_energy_tiles():
	"""生成能源格"""
	var energy_tile_scene = preload("res://scenes/Map/EnergyTile.tscn")
	var total_tiles = map_size.x * map_size.y
	var energy_tile_count = int(total_tiles * Config.ENERGY_TILE_RATIO)
	
	var placed_count = 0
	var attempts = 0
	var max_attempts = energy_tile_count * 10
	
	while placed_count < energy_tile_count and attempts < max_attempts:
		var pos = Vector2i(
			randi() % map_size.x,
			randi() % map_size.y
		)
		
		# 确保不重复放置
		if not energy_tiles.has(pos):
			var energy_tile = energy_tile_scene.instantiate()
			energy_tile.position = grid_to_world(pos)
			energy_tile.grid_position = pos
			
			energy_tiles_container.add_child(energy_tile)
			energy_tiles[pos] = energy_tile
			
			placed_count += 1
		
		attempts += 1
	
	print("Generated ", placed_count, " energy tiles in ", attempts, " attempts")

func world_to_grid(world_pos: Vector2) -> Vector2i:
	"""世界坐标转网格坐标"""
	return Vector2i(
		int(world_pos.x / cell_size),
		int(world_pos.y / cell_size)
	)

func grid_to_world(grid_pos: Vector2i) -> Vector2:
	"""网格坐标转世界坐标（中心点）"""
	return Vector2(
		(grid_pos.x + 0.5) * cell_size,
		(grid_pos.y + 0.5) * cell_size
	)

func is_valid_grid_position(grid_pos: Vector2i) -> bool:
	"""检查网格位置是否有效"""
	return grid_pos.x >= 0 and grid_pos.x < map_size.x and \
		   grid_pos.y >= 0 and grid_pos.y < map_size.y

func find_path(from_world: Vector2, to_world: Vector2) -> PackedVector2Array:
	"""寻找路径（世界坐标）"""
	var from_grid = world_to_grid(from_world)
	var to_grid = world_to_grid(to_world)
	
	if not is_valid_grid_position(from_grid) or not is_valid_grid_position(to_grid):
		return PackedVector2Array()
	
	var path_grid = astar_grid.get_point_path(from_grid, to_grid)
	var path_world = PackedVector2Array()
	
	for point in path_grid:
		path_world.append(grid_to_world(Vector2i(point)))
	
	return path_world

func get_random_corner_position() -> Vector2:
	"""获取随机角落位置（用于放置核心）"""
	var corners = [
		Vector2i(5, 5),  # 左上
		Vector2i(map_size.x - 6, 5),  # 右上
		Vector2i(5, map_size.y - 6),  # 左下
		Vector2i(map_size.x - 6, map_size.y - 6)  # 右下
	]
	
	var corner = corners[randi() % corners.size()]
	# 添加一些随机偏移
	corner += Vector2i(
		randi_range(-3, 3),
		randi_range(-3, 3)
	)
	
	# 确保在地图范围内
	corner.x = clamp(corner.x, 1, map_size.x - 2)
	corner.y = clamp(corner.y, 1, map_size.y - 2)
	
	return grid_to_world(corner)

func find_empty_position_near(center: Vector2, radius: float = 100.0) -> Vector2:
	"""在指定位置附近找到空位"""
	var attempts = 0
	var max_attempts = 50
	
	while attempts < max_attempts:
		var angle = randf() * TAU
		var distance = randf() * radius
		var test_pos = center + Vector2(cos(angle), sin(angle)) * distance
		
		# 检查是否在地图范围内
		var grid_pos = world_to_grid(test_pos)
		if is_valid_grid_position(grid_pos):
			# 简单检查：确保不在能源格上
			if not energy_tiles.has(grid_pos):
				return test_pos
		
		attempts += 1
	
	# 如果找不到空位，返回中心位置
	return center

func occupy_energy_tile(grid_pos: Vector2i, force_id: int):
	"""占领能源格"""
	if energy_tiles.has(grid_pos):
		var old_occupant = energy_tile_occupants.get(grid_pos, -1)
		if old_occupant != force_id:
			energy_tile_occupants[grid_pos] = force_id
			energy_tiles[grid_pos].set_occupant(force_id)
			energy_tile_occupied.emit(grid_pos, force_id)
			
			if old_occupant != -1:
				print("Energy tile at ", grid_pos, " changed from force ", old_occupant, " to ", force_id)

func free_energy_tile(grid_pos: Vector2i):
	"""释放能源格"""
	if energy_tile_occupants.has(grid_pos):
		energy_tile_occupants.erase(grid_pos)
		if energy_tiles.has(grid_pos):
			energy_tiles[grid_pos].set_occupant(-1)
		energy_tile_freed.emit(grid_pos)

func get_energy_tiles_for_force(force_id: int) -> Array[Vector2i]:
	"""获取指定势力占领的能源格"""
	var tiles: Array[Vector2i] = []
	for pos in energy_tile_occupants.keys():
		if energy_tile_occupants[pos] == force_id:
			tiles.append(pos)
	return tiles

func get_total_energy_tiles() -> int:
	"""获取总能源格数量"""
	return energy_tiles.size()

func add_entity(entity: Node2D):
	"""添加实体到地图"""
	entities_container.add_child(entity)
