extends Node2D
class_name Core

# 核心建筑
# 每个势力的根基，负责生产单位和提供能量收入

signal core_destroyed(core: Core)
signal unit_produced(unit: Node2D)

@onready var sprite: Sprite2D = $Sprite2D
@onready var area: Area2D = $Area2D
@onready var collision: CollisionShape2D = $Area2D/CollisionShape2D
@onready var health_bar: ProgressBar = $HealthBar
@onready var level_label: Label = $LevelLabel

var force_id: int = -1
var level: int = 1
var max_hp: float = 0.0
var current_hp: float = 0.0
var defense: float = 0.0

# 生产队列
var production_queue: Array[Blueprint] = []
var current_production: Blueprint = null
var production_timer: float = 0.0

# 能量产出计时器
var energy_timer: float = 0.0
var energy_interval: float = 1.0  # 每秒产出

func _ready():
	# 如果已经初始化过，重新设置外观和属性
	if force_id >= 0:
		setup_appearance()
		setup_stats()

func initialize(force_id_param: int, position_param: Vector2):
	"""初始化核心"""
	force_id = force_id_param
	position = position_param

	# 设置势力引用
	var force = Game.get_force(force_id)
	if force:
		force.core_ref = self

	# 如果节点已经准备好，立即设置外观和属性
	if sprite != null:
		setup_appearance()
		setup_stats()

	print("Core initialized for force ", force_id, " at ", position)

func setup_appearance():
	"""设置外观"""
	# 检查节点是否已准备好
	if not sprite:
		return

	var force = Game.get_force(force_id)
	var color = force.color if force else Color.WHITE

	# 创建圆形精灵
	var image = Image.create(64, 64, false, Image.FORMAT_RGBA8)
	var center = Vector2(32, 32)
	var radius = 28

	for x in range(64):
		for y in range(64):
			var dist = Vector2(x, y).distance_to(center)
			if dist <= radius:
				var alpha = 1.0 - (dist / radius) * 0.3
				image.set_pixel(x, y, Color(color.r, color.g, color.b, alpha))

	var texture = ImageTexture.new()
	texture.set_image(image)
	sprite.texture = texture
	
	# 设置碰撞形状
	if collision:
		var circle_shape = CircleShape2D.new()
		circle_shape.radius = 32
		collision.shape = circle_shape
	
	# 更新等级显示
	update_level_display()

func setup_stats():
	"""设置属性"""
	max_hp = Config.CORE_BASE_HP + (level - 1) * Config.CORE_BASE_HP * 0.2
	current_hp = max_hp
	defense = Config.CORE_BASE_DEFENSE + (level - 1) * Config.CORE_BASE_DEFENSE * 0.2
	
	# 更新血条
	update_health_bar()

func _process(delta):
	"""每帧更新"""
	if Game.current_state != Game.GameState.PLAYING:
		return
	
	# 能量产出
	update_energy_production(delta)
	
	# 生产处理
	update_production(delta)

func update_energy_production(delta: float):
	"""更新能量产出"""
	energy_timer += delta
	if energy_timer >= energy_interval:
		energy_timer = 0.0
		
		var energy_per_sec = Config.CORE_INCOME_PER_SEC_PER_LEVEL * level
		if Game.economy_ref:
			Game.economy_ref.add_energy(force_id, energy_per_sec)

func update_production(delta: float):
	"""更新生产"""
	if current_production == null and not production_queue.is_empty():
		# 开始新的生产
		current_production = production_queue.pop_front()
		production_timer = current_production.get_build_time()
		print("Core ", force_id, " started producing ", current_production.unit_name)
	
	if current_production != null:
		production_timer -= delta
		if production_timer <= 0.0:
			# 生产完成
			spawn_unit(current_production)
			current_production = null

func enqueue_production(blueprint: Blueprint):
	"""加入生产队列"""
	production_queue.append(blueprint)
	print("Core ", force_id, " queued ", blueprint.unit_name, " (queue size: ", production_queue.size(), ")")

func spawn_unit(blueprint: Blueprint):
	"""生成单位"""
	var unit_scene = preload("res://scenes/Entities/Unit.tscn")
	var unit = unit_scene.instantiate()
	
	# 找到空位
	var spawn_pos = find_spawn_position()
	unit.initialize(blueprint, force_id, spawn_pos)
	
	# 添加到地图
	if Game.map_ref:
		Game.map_ref.add_entity(unit)
	
	# 添加到势力单位列表
	var force = Game.get_force(force_id)
	if force:
		force.add_unit(unit)
	
	print("Core ", force_id, " spawned ", blueprint.unit_name, " at ", spawn_pos)
	unit_produced.emit(unit)

func find_spawn_position() -> Vector2:
	"""寻找生成位置"""
	var attempts = 0
	var max_attempts = 20
	var spawn_radius = 80.0
	
	while attempts < max_attempts:
		var angle = randf() * TAU
		var distance = randf_range(50.0, spawn_radius)
		var test_pos = position + Vector2(cos(angle), sin(angle)) * distance
		
		# 检查是否在地图范围内
		if Game.map_ref:
			var grid_pos = Game.map_ref.world_to_grid(test_pos)
			if Game.map_ref.is_valid_grid_position(grid_pos):
				return test_pos
		
		attempts += 1
	
	# 如果找不到合适位置，就在核心旁边生成
	return position + Vector2(50, 0)

func take_damage(damage: float, _source: Node = null):
	"""受到伤害"""
	var actual_damage = max(0, damage - defense)
	current_hp -= actual_damage
	current_hp = max(0, current_hp)
	
	update_health_bar()
	
	print("Core ", force_id, " took ", actual_damage, " damage (", current_hp, "/", max_hp, ")")
	
	if current_hp <= 0:
		destroy()

func destroy():
	"""摧毁核心"""
	print("Core ", force_id, " destroyed!")
	
	# 爆炸伤害
	explode()
	
	# 标记势力为失败
	Game.defeat_force(force_id)
	
	# 发送信号
	core_destroyed.emit(self)
	
	# 移除自己
	queue_free()

func explode():
	"""核心爆炸"""
	var explosion_radius = Config.CORE_EXPLOSION_RADIUS_CELLS * Config.CELL_SIZE
	var center_damage = Config.CORE_EXPLOSION_DAMAGE_CENTER
	
	# 获取爆炸范围内的所有单位
	var space_state = get_world_2d().direct_space_state
	var query = PhysicsShapeQueryParameters2D.new()
	var circle_shape = CircleShape2D.new()
	circle_shape.radius = explosion_radius
	query.shape = circle_shape
	query.transform.origin = position
	
	var results = space_state.intersect_shape(query)
	
	for result in results:
		var body = result.collider
		if body.has_method("take_damage"):
			var distance = position.distance_to(body.position)
			var damage_ratio = 1.0 - (distance / explosion_radius)
			var damage = center_damage * damage_ratio
			body.take_damage(damage, self)
			print("Explosion damaged ", body.name, " for ", damage)

func upgrade():
	"""升级核心"""
	if level >= Config.CORE_MAX_LEVEL:
		return false
	
	var cost = Config.CORE_UPGRADE_COST_FACTOR * level * level
	
	if Game.economy_ref and Game.economy_ref.spend_energy(force_id, cost):
		level += 1
		setup_stats()
		setup_appearance()
		print("Core ", force_id, " upgraded to level ", level)
		return true
	
	return false

func get_upgrade_cost() -> int:
	"""获取升级费用"""
	if level >= Config.CORE_MAX_LEVEL:
		return -1
	return Config.CORE_UPGRADE_COST_FACTOR * level * level

func update_health_bar():
	"""更新血条"""
	if health_bar:
		health_bar.max_value = max_hp
		health_bar.value = current_hp
		health_bar.visible = current_hp < max_hp

func update_level_display():
	"""更新等级显示"""
	if level_label:
		level_label.text = "Lv." + str(level)

func get_force_id() -> int:
	"""获取势力ID"""
	return force_id

func get_production_queue_size() -> int:
	"""获取生产队列大小"""
	var size = production_queue.size()
	if current_production != null:
		size += 1
	return size
