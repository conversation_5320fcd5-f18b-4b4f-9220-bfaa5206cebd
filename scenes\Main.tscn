[gd_scene load_steps=7 format=3 uid="uid://c2t6e1pfug7fp"]

[ext_resource type="Script" uid="uid://deqyku3s5l4wp" path="res://scenes/Main.gd" id="1_6f6f6"]
[ext_resource type="PackedScene" uid="uid://bqxvhqxqxqxqx" path="res://scenes/Map/Map.tscn" id="2_7g7g7"]
[ext_resource type="PackedScene" uid="uid://cgqgd2pofrsn0" path="res://scenes/UI/HUD.tscn" id="3_8h8h8"]
[ext_resource type="Script" uid="uid://cabby0n71buvl" path="res://systems/Production.gd" id="4_9i9i9"]
[ext_resource type="Script" uid="uid://da4gw114owjk6" path="res://systems/Economy.gd" id="5_0j0j0"]
[ext_resource type="Script" uid="uid://dgcwkpdimi4kf" path="res://systems/Victory.gd" id="6_1k1k1"]

[node name="Main" type="Node2D"]
script = ExtResource("1_6f6f6")

[node name="Map" parent="." instance=ExtResource("2_7g7g7")]

[node name="Camera2D" type="Camera2D" parent="."]

[node name="UI" type="CanvasLayer" parent="."]

[node name="HUD" parent="UI" instance=ExtResource("3_8h8h8")]
grow_horizontal = 2
grow_vertical = 2

[node name="Systems" type="Node" parent="."]

[node name="ProductionSystem" type="Node" parent="Systems"]
script = ExtResource("4_9i9i9")

[node name="EconomySystem" type="Node" parent="Systems"]
script = ExtResource("5_0j0j0")

[node name="VictorySystem" type="Node" parent="Systems"]
script = ExtResource("6_1k1k1")
