extends CharacterBody2D
class_name Unit

# 游戏单位
# 具有移动、战斗、升级等功能的基础单位

signal unit_died(unit: Unit)
signal unit_leveled_up(unit: Unit, new_level: int)
signal unit_killed_enemy(unit: Unit, victim: Unit)

# 单位状态
enum State {
	IDLE,
	SEEKING,
	CHASING,
	ATTACKING,
	MOVING_TO_COMMAND  # 手动命令移动
}

@onready var polygon: Polygon2D = $Polygon2D
@onready var area: Area2D = $Area2D
@onready var collision: CollisionShape2D = $Area2D/CollisionShape2D
@onready var health_bar: ProgressBar = $HealthBar
@onready var level_label: Label = $LevelLabel

# 基础属性
var blueprint: Blueprint
var force_id: int = -1
var level: int = 1

# 当前属性（考虑升级加成）
var max_hp: float = 0.0
var current_hp: float = 0.0
var attack: float = 0.0
var defense: float = 0.0
var attack_speed: float = 1.0
var attack_range: float = 0.0
var move_speed: float = 0.0

# 战斗相关
var attack_cooldown: float = 0.0
var target: Node2D = null
var kills: int = 0
var alive_time: float = 0.0

# 移动相关
var current_state: State = State.IDLE
var path: PackedVector2Array = PackedVector2Array()
var path_index: int = 0
var last_pathfind_time: float = 0.0
var command_target_position: Vector2 = Vector2.ZERO

# 感知相关
var detection_area: Area2D
var enemies_in_range: Array[Node] = []

func _ready():
	# 连接信号
	if area:
		area.area_entered.connect(_on_area_entered)
		area.area_exited.connect(_on_area_exited)

	# 如果已经初始化过，重新设置
	if blueprint != null:
		setup_detection_area()
		setup_appearance()
		setup_collision()

func initialize(blueprint_param: Blueprint, force_id_param: int, position_param: Vector2):
	"""初始化单位"""
	blueprint = blueprint_param
	force_id = force_id_param
	position = position_param
	level = 1

	# 计算属性
	calculate_stats()

	# 如果节点已经准备好，立即设置
	if polygon != null:
		setup_appearance()
		setup_collision()
		setup_detection_area()

	print("Unit ", blueprint.unit_name, " initialized for force ", force_id, " at ", position)

func calculate_stats():
	"""计算当前属性（包含升级加成）"""
	var level_multiplier_major = 1.0 + (level - 1) * Config.UNIT_LEVEL_HP_GROWTH
	var level_multiplier_minor = 1.0 + (level - 1) * Config.UNIT_LEVEL_SPEED_GROWTH
	
	max_hp = blueprint.get_stat("hp") * level_multiplier_major
	attack = blueprint.get_stat("attack") * level_multiplier_major
	defense = blueprint.get_stat("defense") * level_multiplier_major
	attack_speed = blueprint.get_stat("attack_speed")  # 攻击速度不受等级影响
	attack_range = blueprint.get_stat("attack_range") * level_multiplier_minor
	move_speed = blueprint.get_stat("speed") * level_multiplier_minor
	
	# 如果是新单位，设置满血
	if current_hp == 0.0:
		current_hp = max_hp
	else:
		# 升级时按比例增加当前血量
		current_hp = min(current_hp * level_multiplier_major, max_hp)
	
	update_health_bar()

func setup_appearance():
	"""设置外观"""
	# 检查节点是否已准备好
	if not polygon:
		return

	var force = Game.get_force(force_id)
	var color = force.color if force else Color.WHITE

	# 计算大小（基于等级）
	var base_size = 16.0
	var size_multiplier = 1.0 + (level - 1) * Config.UNIT_LEVEL_SIZE_GROWTH
	var radius = base_size * size_multiplier

	# 生成形状顶点
	var points = Math2D.generate_shape_points(blueprint.shape, radius)
	polygon.polygon = points
	polygon.color = color

	# 设置描边（根据等级）
	if level >= 2:
		polygon.outline_color = Color.YELLOW if level == 2 else Color.RED
		polygon.outline_width = 2.0
	else:
		polygon.outline_width = 0.0

	# 更新等级显示
	update_level_display()

func setup_collision():
	"""设置碰撞形状"""
	# 检查节点是否已准备好
	if not collision:
		return

	var base_size = 16.0
	var size_multiplier = 1.0 + (level - 1) * Config.UNIT_LEVEL_SIZE_GROWTH
	var radius = base_size * size_multiplier

	var circle_shape = CircleShape2D.new()
	circle_shape.radius = radius
	collision.shape = circle_shape

func setup_detection_area():
	"""设置检测区域"""
	detection_area = Area2D.new()
	var detection_collision = CollisionShape2D.new()
	var detection_shape = CircleShape2D.new()
	
	detection_shape.radius = attack_range * Config.CELL_SIZE + 50  # 攻击距离+缓冲
	detection_collision.shape = detection_shape
	detection_area.add_child(detection_collision)
	add_child(detection_area)
	
	detection_area.area_entered.connect(_on_detection_area_entered)
	detection_area.area_exited.connect(_on_detection_area_exited)

func _physics_process(delta):
	"""物理更新"""
	if Game.current_state != Game.GameState.PLAYING:
		return
	
	# 更新存活时间
	alive_time += delta
	
	# 更新攻击冷却
	if attack_cooldown > 0:
		attack_cooldown -= delta
	
	# 状态机更新
	update_state_machine(delta)
	
	# 移动处理
	handle_movement(delta)
	
	# 检查升级条件
	check_level_up()

func update_state_machine(_delta: float):
	"""更新状态机"""
	match current_state:
		State.IDLE:
			handle_idle_state()
		State.SEEKING:
			handle_seeking_state()
		State.CHASING:
			handle_chasing_state()
		State.ATTACKING:
			handle_attacking_state()
		State.MOVING_TO_COMMAND:
			handle_command_move_state()

func handle_idle_state():
	"""处理空闲状态"""
	# 寻找最近的敌人
	find_nearest_enemy()
	if target:
		current_state = State.SEEKING

func handle_seeking_state():
	"""处理寻敌状态"""
	if not target or not is_instance_valid(target):
		target = null
		current_state = State.IDLE
		return
	
	var distance = position.distance_to(target.position)
	var attack_range_world = attack_range * Config.CELL_SIZE
	
	if distance <= attack_range_world:
		current_state = State.ATTACKING
	else:
		# 寻路到目标
		var current_time = Time.get_time_dict_from_system().get("unix", 0)
		if current_time - last_pathfind_time > Config.AI_PATHFIND_INTERVAL:
			find_path_to_target()
			last_pathfind_time = current_time
		current_state = State.CHASING

func handle_chasing_state():
	"""处理追击状态"""
	if not target or not is_instance_valid(target):
		target = null
		current_state = State.IDLE
		return
	
	var distance = position.distance_to(target.position)
	var attack_range_world = attack_range * Config.CELL_SIZE
	
	if distance <= attack_range_world:
		current_state = State.ATTACKING
		path.clear()  # 清除路径，停止移动

func handle_attacking_state():
	"""处理攻击状态"""
	if not target or not is_instance_valid(target):
		target = null
		current_state = State.IDLE
		return
	
	var distance = position.distance_to(target.position)
	var attack_range_world = attack_range * Config.CELL_SIZE
	
	if distance > attack_range_world * 1.2:  # 添加一些容差
		current_state = State.CHASING
		return
	
	# 执行攻击
	if attack_cooldown <= 0:
		perform_attack()

func handle_command_move_state():
	"""处理命令移动状态"""
	if path.is_empty():
		current_state = State.IDLE

func handle_movement(_delta: float):
	"""处理移动"""
	if path.is_empty():
		velocity = Vector2.ZERO
		move_and_slide()
		return
	
	# 沿路径移动
	if path_index >= path.size():
		path.clear()
		path_index = 0
		velocity = Vector2.ZERO
		move_and_slide()
		return
	
	var target_pos = path[path_index]
	var direction = (target_pos - position).normalized()
	var distance = position.distance_to(target_pos)
	
	if distance < 10.0:  # 到达当前路径点
		path_index += 1
		if path_index >= path.size():
			path.clear()
			path_index = 0
	else:
		velocity = direction * move_speed
	
	move_and_slide()

func find_nearest_enemy():
	"""寻找最近的敌人"""
	var nearest_enemy: Node2D = null
	var nearest_distance = INF
	
	# 检查范围内的敌人
	for enemy in enemies_in_range:
		if not is_instance_valid(enemy):
			continue
		
		if enemy.has_method("get_force_id"):
			var enemy_force_id = enemy.get_force_id()
			if is_enemy_force(enemy_force_id):
				var distance = position.distance_to(enemy.position)
				if distance < nearest_distance:
					nearest_distance = distance
					nearest_enemy = enemy
	
	# 如果没有找到敌人，寻找敌方核心
	if not nearest_enemy:
		for enemy_force_id in Game.forces.keys():
			if is_enemy_force(enemy_force_id):
				var force = Game.get_force(enemy_force_id)
				if force and force.core_ref and is_instance_valid(force.core_ref):
					var distance = position.distance_to(force.core_ref.position)
					if distance < nearest_distance:
						nearest_distance = distance
						nearest_enemy = force.core_ref
	
	target = nearest_enemy

func find_path_to_target():
	"""寻找到目标的路径"""
	if not target or not Game.map_ref:
		return
	
	path = Game.map_ref.find_path(position, target.position)
	path_index = 0

func perform_attack():
	"""执行攻击"""
	if not target or not is_instance_valid(target):
		return
	
	attack_cooldown = 1.0 / attack_speed
	
	# 计算伤害
	var damage = max(0, attack - (target.defense if target.has_method("get_defense") else 0))
	
	# 造成伤害
	if target.has_method("take_damage"):
		target.take_damage(damage, self)
		print(blueprint.unit_name, " attacked ", target.name, " for ", damage, " damage")
		
		# 检查目标是否死亡
		if target.has_method("is_dead") and target.is_dead():
			on_enemy_killed(target)

func on_enemy_killed(victim: Node):
	"""敌人被击杀"""
	kills += 1
	unit_killed_enemy.emit(self, victim)
	
	# 获得赏金
	if Game.economy_ref and victim.has_method("get_blueprint"):
		var victim_blueprint = victim.get_blueprint()
		var victim_level = victim.level if victim.has_method("get_level") else 1
		Game.economy_ref.grant_bounty(force_id, victim_blueprint, victim_level)
	
	# 清除目标
	target = null
	current_state = State.IDLE

func take_damage(damage: float, _source: Node = null):
	"""受到伤害"""
	current_hp -= damage
	current_hp = max(0, current_hp)
	
	update_health_bar()
	
	if current_hp <= 0:
		die()

func die():
	"""死亡"""
	print(blueprint.unit_name, " died")
	
	# 从势力单位列表中移除
	var force = Game.get_force(force_id)
	if force:
		force.remove_unit(self)
	
	unit_died.emit(self)
	queue_free()

func check_level_up():
	"""检查升级条件"""
	if level >= Config.UNIT_MAX_LEVEL:
		return
	
	var can_level_up = false
	
	# 击杀数升级
	if kills >= Config.UNIT_UPGRADE_KILLS_REQUIRED:
		can_level_up = true
	
	# 存活时间升级
	if alive_time >= Config.UNIT_UPGRADE_SURVIVAL_TIME:
		can_level_up = true
	
	if can_level_up:
		level_up()

func level_up():
	"""升级"""
	level += 1
	
	# 重新计算属性
	calculate_stats()
	
	# 更新外观
	setup_appearance()
	setup_collision()
	
	print(blueprint.unit_name, " leveled up to ", level)
	unit_leveled_up.emit(self, level)

func is_enemy_force(other_force_id: int) -> bool:
	"""检查是否为敌对势力"""
	var my_force = Game.get_force(force_id)
	var other_force = Game.get_force(other_force_id)
	
	if not my_force or not other_force:
		return false
	
	return my_force.team_id != other_force.team_id

func _on_detection_area_entered(area_node: Area2D):
	"""检测区域进入"""
	var body = area_node.get_parent()
	if body != self and body.has_method("get_force_id"):
		enemies_in_range.append(body)

func _on_detection_area_exited(area_node: Area2D):
	"""检测区域离开"""
	var body = area_node.get_parent()
	if body in enemies_in_range:
		enemies_in_range.erase(body)

func _on_area_entered(_area_node: Area2D):
	"""单位区域进入（用于能源格检测）"""
	pass

func _on_area_exited(_area_node: Area2D):
	"""单位区域离开"""
	pass

# 命令接口
func move_to_position(target_position: Vector2):
	"""移动到指定位置"""
	command_target_position = target_position
	if Game.map_ref:
		path = Game.map_ref.find_path(position, target_position)
		path_index = 0
		current_state = State.MOVING_TO_COMMAND

func attack_target(new_target: Node2D):
	"""攻击指定目标"""
	if new_target and new_target.has_method("get_force_id"):
		var target_force_id = new_target.get_force_id()
		if is_enemy_force(target_force_id):
			target = new_target
			current_state = State.SEEKING

# 获取器方法
func get_force_id() -> int:
	return force_id

func get_blueprint() -> Blueprint:
	return blueprint

func get_level() -> int:
	return level

func is_dead() -> bool:
	return current_hp <= 0

func get_defense() -> float:
	return defense

func update_health_bar():
	"""更新血条"""
	if health_bar:
		health_bar.max_value = max_hp
		health_bar.value = current_hp
		health_bar.visible = current_hp < max_hp

func update_level_display():
	"""更新等级显示"""
	if level_label and level > 1:
		level_label.text = "Lv." + str(level)
		level_label.visible = true
	elif level_label:
		level_label.visible = false
