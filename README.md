# 几何战争 2 (Geometric Warfare 2)

## 项目状态：里程碑 1 完成

这是一个基于 Godot 4 开发的 2D 即时战略游戏原型。

### 已实现功能

#### 核心系统
- ✅ 全局游戏状态管理 (Game.gd)
- ✅ 配置系统 (Config.gd)
- ✅ 势力和队伍管理
- ✅ 地图系统 (100x100 网格，能源格)
- ✅ 寻路系统 (AStarGrid2D)

#### 单位系统
- ✅ 5种几何形状单位 (圆形、三角形、正方形、五边形、六边形)
- ✅ 单位蓝图系统 (Blueprint.gd)
- ✅ 单位属性系统 (生命、攻击、防御、速度等)
- ✅ 单位升级系统 (最高3级)
- ✅ 单位 AI 行为 (寻敌、追击、攻击)

#### 建筑系统
- ✅ 核心建筑 (Core)
- ✅ 核心升级系统 (1-10级)
- ✅ 核心爆炸机制
- ✅ 单位生产系统

#### 经济系统
- ✅ 能量资源管理
- ✅ 核心能量产出
- ✅ 能源格占领机制
- ✅ 击杀赏金系统

#### 胜利条件
- ✅ 征服模式 (摧毁敌方核心)
- ✅ 统治模式 (占领50%能源格)
- ✅ 弑君模式 (占领奇点)

#### 用户界面
- ✅ 基础 HUD (资源、时间显示)
- ✅ 生产面板 (5个单位按钮)
- ✅ 信息面板 (胜利进度、生产队列)
- ✅ 基础摄像机控制

### 游戏操作

#### 键盘控制
- `WASD` 或 `方向键`: 移动摄像机
- `1-5`: 快捷键生产对应单位
- `鼠标滚轮`: 缩放摄像机
- `ESC`: 暂停/菜单 (预留)
- `空格`: 暂停/继续 (预留)

#### 鼠标控制
- `左键`: 选择单位/建筑
- `右键`: 移动/攻击命令 (预留)

### 默认单位

| 形状 | 名称 | 定位 | 特点 |
|------|------|------|------|
| ● 圆形 | Node | 基础全能单位 | 平衡的属性，适合大规模作战 |
| ▲ 三角形 | Vanguard | 快速突击单位 | 高速度，低防御 |
| ■ 正方形 | Bulwark | 重装坦克单位 | 高生命和防御 |
| ⬠ 五边形 | Marksman | 远程输出单位 | 远攻击距离，高攻击力 |
| ⬢ 六边形 | Coordinator | 辅助单位 | 平衡属性，预留技能扩展 |

### 技术架构

#### 目录结构
```
res://
├── autoload/           # 全局单例
├── scenes/            # 场景文件
│   ├── Map/           # 地图相关
│   ├── Entities/      # 实体 (单位、建筑)
│   └── UI/            # 用户界面
├── systems/           # 游戏系统
├── models/            # 数据模型
├── utils/             # 工具类
└── data/              # 游戏数据
    └── blueprints/    # 单位蓝图
```

#### 核心系统
- **Game.gd**: 全局状态管理，势力注册，游戏流程控制
- **Config.gd**: 所有游戏常量和配置参数
- **Production.gd**: 单位生产和队列管理
- **Economy.gd**: 资源管理和经济计算
- **Victory.gd**: 胜利条件判定
- **Map.gd**: 地图管理和寻路服务

### 开发计划

#### 里程碑 2 (计划中)
- [ ] 核心升级与爆炸效果
- [ ] 单位升级外观变化
- [ ] 统治和弑君模式完整实现
- [ ] 5分钟巢穴事件
- [ ] 12分钟终极目标事件
- [ ] 小地图实现
- [ ] 更智能的 AI

#### 里程碑 3 (计划中)
- [ ] 单位技能系统
- [ ] 复杂微操控制
- [ ] 多人游戏支持
- [ ] 自定义蓝图编辑器
- [ ] 音效和视觉效果
- [ ] 性能优化

### 如何运行

1. 使用 Godot 4.2+ 打开项目
2. 运行 `scenes/Main.tscn` 场景
3. 游戏将自动开始，玩家(蓝色)对战 AI(红色)

### 测试

项目包含系统测试脚本 `test_systems.gd`，可以验证核心功能是否正常工作。

### 配置调整

所有游戏参数都在 `autoload/Config.gd` 中定义，可以轻松调整：
- 地图大小
- 单位属性范围
- 经济参数
- 胜利条件参数
- AI 行为参数

### 已知问题

- 单位可能会重叠 (简化的碰撞系统)
- AI 较为简单 (随机生产)
- 缺少音效和粒子效果
- 性能未针对大规模战斗优化

### 贡献

这是一个学习项目，欢迎提出改进建议和 bug 报告。
