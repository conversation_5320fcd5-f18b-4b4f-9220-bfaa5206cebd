extends Control
class_name HUD

# 主游戏界面
# 显示资源、时间、生产面板等

signal unit_production_requested(blueprint: Blueprint)
signal game_menu_requested

@onready var top_bar: Control = $TopBar
@onready var energy_label: Label = $TopBar/EnergyLabel
@onready var time_label: Label = $TopBar/TimeLabel
@onready var menu_button: Button = $TopBar/MenuButton

@onready var production_panel: Control = $ProductionPanel
@onready var production_buttons: Array[Button] = []

@onready var info_panel: Control = $InfoPanel
@onready var info_label: Label = $InfoPanel/InfoLabel

var player_force_id: int = -1
var current_blueprints: Array[Blueprint] = []

func _ready():
	# 设置 HUD 引用
	Game.hud_ref = self
	
	# 连接信号
	if menu_button:
		menu_button.pressed.connect(_on_menu_button_pressed)
	
	# 设置生产按钮
	setup_production_buttons()
	
	# 连接经济系统信号
	if Game.economy_ref:
		Game.economy_ref.energy_changed.connect(_on_energy_changed)
	
	print("HUD initialized")

func initialize(force_id: int):
	"""初始化 HUD"""
	player_force_id = force_id
	
	# 获取默认蓝图
	if Game.production_ref:
		current_blueprints = Game.production_ref.get_all_default_blueprints()
		update_production_buttons()
	
	# 初始显示
	update_display()

func setup_production_buttons():
	"""设置生产按钮"""
	# 创建5个生产按钮
	for i in range(5):
		var button = Button.new()
		button.custom_minimum_size = Vector2(80, 80)
		button.text = "Unit " + str(i + 1)
		button.pressed.connect(_on_production_button_pressed.bind(i))
		
		production_panel.add_child(button)
		production_buttons.append(button)

func update_production_buttons():
	"""更新生产按钮"""
	for i in range(min(production_buttons.size(), current_blueprints.size())):
		var button = production_buttons[i]
		var blueprint = current_blueprints[i]
		
		# 设置按钮文本
		var text = blueprint.unit_name + "\n"
		text += str(blueprint.get_price()) + " Energy\n"
		text += "%.1fs" % blueprint.get_build_time()
		button.text = text
		
		# 检查是否可以生产
		var can_produce = false
		if Game.production_ref:
			can_produce = Game.production_ref.can_produce(player_force_id, blueprint)
		
		button.disabled = not can_produce

func update_display():
	"""更新显示"""
	# 更新能量显示
	update_energy_display()
	
	# 更新时间显示
	update_time_display()
	
	# 更新生产按钮
	update_production_buttons()
	
	# 更新信息面板
	update_info_panel()

func update_energy_display():
	"""更新能量显示"""
	if not energy_label:
		return
	
	var energy = 0
	var income_rate = 0.0
	
	if Game.economy_ref:
		energy = Game.economy_ref.get_energy(player_force_id)
		income_rate = Game.economy_ref.get_total_energy_income_rate(player_force_id)
	
	energy_label.text = "Energy: " + str(energy) + " (+" + "%.1f" % income_rate + "/s)"

func update_time_display():
	"""更新时间显示"""
	if not time_label:
		return
	
	var minutes = int(Game.game_time / 60)
	var seconds = int(Game.game_time) % 60
	time_label.text = "%02d:%02d" % [minutes, seconds]

func update_info_panel():
	"""更新信息面板"""
	if not info_label:
		return
	
	var info_text = ""
	
	# 显示胜利条件信息
	if Game.victory_ref:
		var victory_info = Game.victory_ref.get_victory_info()
		info_text += "Victory: " + victory_info.type + "\n"
		
		if victory_info.has("progress") and victory_info.progress.has(get_player_team_id()):
			var progress = victory_info.progress[get_player_team_id()]
			info_text += "Progress: " + "%.1f" % (progress * 100) + "%\n"
	
	# 显示生产信息
	if Game.production_ref:
		var production_info = Game.production_ref.get_production_info(player_force_id)
		if production_info.has("queue_size") and production_info.queue_size > 0:
			info_text += "Production Queue: " + str(production_info.queue_size) + "\n"
			
			if production_info.has("current_production") and production_info.current_production:
				info_text += "Building: " + production_info.current_production + "\n"
				if production_info.has("current_progress"):
					info_text += "Progress: " + "%.1f" % (production_info.current_progress * 100) + "%\n"
	
	# 显示单位统计
	var force = Game.get_force(player_force_id)
	if force:
		info_text += "Units: " + str(force.units.size()) + "/" + str(Config.MAX_UNITS_PER_FORCE) + "\n"
	
	info_label.text = info_text

func get_player_team_id() -> int:
	"""获取玩家队伍ID"""
	var force = Game.get_force(player_force_id)
	return force.team_id if force else -1

func _on_production_button_pressed(button_index: int):
	"""生产按钮被按下"""
	if button_index >= 0 and button_index < current_blueprints.size():
		var blueprint = current_blueprints[button_index]
		unit_production_requested.emit(blueprint)
		
		# 请求生产
		if Game.production_ref:
			Game.production_ref.request_production(player_force_id, blueprint)

func _on_menu_button_pressed():
	"""菜单按钮被按下"""
	game_menu_requested.emit()

func _on_energy_changed(force_id: int, new_amount: int):
	"""能量变化"""
	if force_id == player_force_id:
		update_energy_display()
		update_production_buttons()

# 输入处理
func _input(event):
	"""处理输入"""
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
			handle_left_click(event.position)
		elif event.button_index == MOUSE_BUTTON_RIGHT and event.pressed:
			handle_right_click(event.position)

func handle_left_click(screen_pos: Vector2):
	"""处理左键点击"""
	# 转换为世界坐标
	var world_pos = get_viewport().get_camera_2d().get_global_mouse_position()
	
	# 查找点击的对象
	var space_state = get_world_2d().direct_space_state
	var query = PhysicsPointQueryParameters2D.new()
	query.position = world_pos
	query.collision_mask = 1  # 假设所有可选择对象在第1层
	
	var results = space_state.intersect_point(query)
	
	if not results.is_empty():
		var clicked_object = results[0].collider
		select_object(clicked_object)

func handle_right_click(screen_pos: Vector2):
	"""处理右键点击"""
	var world_pos = get_viewport().get_camera_2d().get_global_mouse_position()
	
	# 这里可以实现移动命令或攻击命令
	# 暂时简化处理
	print("Right click at ", world_pos)

func select_object(object: Node):
	"""选择对象"""
	if object.has_method("get_force_id"):
		var force_id = object.get_force_id()
		if force_id == player_force_id:
			print("Selected friendly object: ", object.name)
			# 这里可以显示对象详细信息
		else:
			print("Selected enemy object: ", object.name)

# 消息显示
func show_message(message: String, duration: float = 3.0):
	"""显示消息"""
	print("Message: ", message)
	# 这里可以实现消息弹窗

func show_victory_message(winner_team_id: int, victory_type: String):
	"""显示胜利消息"""
	var player_team_id = get_player_team_id()
	var message = ""
	
	if winner_team_id == player_team_id:
		message = "Victory! You won by " + victory_type
	else:
		message = "Defeat! Enemy won by " + victory_type
	
	show_message(message, 10.0)
