extends Node2D
class_name EnergyTile

# 能源格
# 可被单位占领，为占领方提供能量收入

@onready var sprite: Sprite2D = $Sprite2D
@onready var area: Area2D = $Area2D
@onready var collision: CollisionShape2D = $Area2D/CollisionShape2D

var grid_position: Vector2i
var occupant_force_id: int = -1
var units_in_range: Array[Node] = []

func _ready():
	# 设置外观
	setup_appearance()
	
	# 连接信号
	area.body_entered.connect(_on_unit_entered)
	area.body_exited.connect(_on_unit_exited)
	area.area_entered.connect(_on_area_entered)
	area.area_exited.connect(_on_area_exited)

func setup_appearance():
	"""设置能源格外观"""
	# 创建矩形形状
	var rect = RectangleShape2D.new()
	rect.size = Vector2(Config.CELL_SIZE * 0.8, Config.CELL_SIZE * 0.8)
	collision.shape = rect
	
	# 创建精灵
	if not sprite:
		sprite = Sprite2D.new()
		add_child(sprite)
	
	# 创建纹理（简单的彩色矩形）
	var image = Image.create(Config.CELL_SIZE, Config.CELL_SIZE, false, Image.FORMAT_RGBA8)
	image.fill(Color(1.0, 1.0, 0.3, 0.6))  # 半透明黄色
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	sprite.texture = texture
	
	# 设置缩放
	sprite.scale = Vector2(0.8, 0.8)

func _on_unit_entered(body):
	"""单位进入范围"""
	if body.has_method("get_force_id"):
		units_in_range.append(body)
		_update_occupant()

func _on_unit_exited(body):
	"""单位离开范围"""
	if body in units_in_range:
		units_in_range.erase(body)
		_update_occupant()

func _on_area_entered(area_node):
	"""区域进入（用于检测单位的 Area2D）"""
	var body = area_node.get_parent()
	if body and body.has_method("get_force_id"):
		units_in_range.append(body)
		_update_occupant()

func _on_area_exited(area_node):
	"""区域离开"""
	var body = area_node.get_parent()
	if body in units_in_range:
		units_in_range.erase(body)
		_update_occupant()

func _update_occupant():
	"""更新占领者"""
	# 清理无效的单位引用
	units_in_range = units_in_range.filter(func(unit): return is_instance_valid(unit))
	
	if units_in_range.is_empty():
		# 没有单位，释放占领
		if occupant_force_id != -1:
			set_occupant(-1)
			if Game.map_ref:
				Game.map_ref.free_energy_tile(grid_position)
	else:
		# 有单位，选择占领者（简单规则：第一个进入的单位的势力）
		var first_unit = units_in_range[0]
		if first_unit.has_method("get_force_id"):
			var force_id = first_unit.get_force_id()
			if force_id != occupant_force_id:
				set_occupant(force_id)
				if Game.map_ref:
					Game.map_ref.occupy_energy_tile(grid_position, force_id)

func set_occupant(force_id: int):
	"""设置占领者"""
	occupant_force_id = force_id
	_update_appearance()

func _update_appearance():
	"""更新外观以反映占领状态"""
	if not sprite:
		return
	
	var base_color = Color(1.0, 1.0, 0.3, 0.6)  # 默认黄色
	
	if occupant_force_id >= 0:
		# 被占领，显示势力颜色
		var force = Game.get_force(occupant_force_id)
		if force:
			base_color = force.color
			base_color.a = 0.8  # 更不透明
	
	# 重新创建纹理
	var image = Image.create(Config.CELL_SIZE, Config.CELL_SIZE, false, Image.FORMAT_RGBA8)
	image.fill(base_color)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	sprite.texture = texture

func get_occupant_force_id() -> int:
	"""获取占领者势力ID"""
	return occupant_force_id

func is_occupied() -> bool:
	"""是否被占领"""
	return occupant_force_id >= 0
