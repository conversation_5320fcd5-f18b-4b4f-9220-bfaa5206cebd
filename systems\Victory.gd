extends Node
class_name VictorySystem

# 胜利条件系统
# 管理三种胜利模式：征服、统治、弑君

signal victory_achieved(winner_team_id: int, victory_type: String)
signal victory_progress_changed(team_id: int, progress: float, victory_type: String)

enum VictoryType {
	CONQUEST,
	DOMINATION,
	KING_OF_THE_HILL
}

var current_victory_type: VictoryType = VictoryType.CONQUEST
var victory_achieved_flag: bool = false

# 统治模式相关
var domination_scores: Dictionary = {}  # team_id -> score

# 弑君模式相关
var koth_scores: Dictionary = {}  # team_id -> score
var singularity_position: Vector2 = Vector2.ZERO
var singularity_active: bool = false
var singularity_occupant_team: int = -1

func _ready():
	# 设置系统引用
	Game.victory_ref = self
	
	print("Victory system initialized")

func set_victory_type(victory_type: String):
	"""设置胜利条件类型"""
	match victory_type.to_lower():
		"conquest":
			current_victory_type = VictoryType.CONQUEST
		"domination":
			current_victory_type = VictoryType.DOMINATION
		"koth", "king_of_the_hill":
			current_victory_type = VictoryType.KING_OF_THE_HILL
		_:
			current_victory_type = VictoryType.CONQUEST
	
	# 初始化分数
	initialize_scores()
	
	print("Victory type set to: ", victory_type)

func initialize_scores():
	"""初始化分数"""
	domination_scores.clear()
	koth_scores.clear()
	
	for team_id in Game.teams.keys():
		domination_scores[team_id] = 0.0
		koth_scores[team_id] = 0.0

func update(delta: float):
	"""更新胜利条件"""
	if victory_achieved_flag:
		return
	
	match current_victory_type:
		VictoryType.CONQUEST:
			update_conquest()
		VictoryType.DOMINATION:
			update_domination(delta)
		VictoryType.KING_OF_THE_HILL:
			update_king_of_the_hill(delta)

func update_conquest():
	"""更新征服模式"""
	# 检查是否有队伍的所有敌对核心都被摧毁
	var active_teams: Array[int] = []
	
	for team_id in Game.teams.keys():
		var team = Game.get_team(team_id)
		if not team:
			continue
		
		var has_active_core = false
		for force_id in team.force_ids:
			var force = Game.get_force(force_id)
			if force and not force.is_defeated and force.core_ref and is_instance_valid(force.core_ref):
				has_active_core = true
				break
		
		if has_active_core:
			active_teams.append(team_id)
	
	# 如果只剩一个队伍，该队伍获胜
	if active_teams.size() == 1:
		achieve_victory(active_teams[0], "conquest")
	elif active_teams.size() == 0:
		# 平局？选择第一个队伍获胜（不应该发生）
		if not Game.teams.is_empty():
			achieve_victory(Game.teams.keys()[0], "conquest")

func update_domination(delta: float):
	"""更新统治模式"""
	if not Game.economy_ref:
		return
	
	# 更新每个队伍的分数
	for team_id in Game.teams.keys():
		var team = Game.get_team(team_id)
		if not team:
			continue
		
		var energy_ratio = Game.economy_ref.get_team_energy_tile_ratio(team_id)
		var current_score = domination_scores.get(team_id, 0.0)
		
		if energy_ratio > Config.DOMINATION_THRESHOLD:
			# 占领超过阈值，增加分数
			var gain = Config.DOMINATION_GAIN_FACTOR * energy_ratio * delta
			current_score += gain
		else:
			# 未达到阈值，减少分数
			current_score -= Config.VICTORY_DECAY_RATE * delta
			current_score = max(0.0, current_score)
		
		domination_scores[team_id] = current_score
		victory_progress_changed.emit(team_id, current_score / Config.VICTORY_TARGET_SCORE, "domination")
		
		# 检查胜利条件
		if current_score >= Config.VICTORY_TARGET_SCORE:
			achieve_victory(team_id, "domination")
			return

func update_king_of_the_hill(delta: float):
	"""更新弑君模式"""
	if not singularity_active:
		return
	
	# 检查奇点占领情况
	var occupant_team = get_singularity_occupant()
	
	# 更新分数
	for team_id in Game.teams.keys():
		var current_score = koth_scores.get(team_id, 0.0)
		
		if team_id == occupant_team:
			# 占领奇点，增加分数
			current_score += Config.KOTH_GAIN_RATE * delta
		else:
			# 未占领，减少分数
			current_score -= Config.VICTORY_DECAY_RATE * delta
			current_score = max(0.0, current_score)
		
		koth_scores[team_id] = current_score
		victory_progress_changed.emit(team_id, current_score / Config.VICTORY_TARGET_SCORE, "koth")
		
		# 检查胜利条件
		if current_score >= Config.VICTORY_TARGET_SCORE:
			achieve_victory(team_id, "koth")
			return

func get_singularity_occupant() -> int:
	"""获取奇点占领者队伍"""
	if not singularity_active or not Game.map_ref:
		return -1
	
	# 检查奇点周围的单位
	var singularity_radius = 50.0  # 奇点控制半径
	var occupant_forces: Dictionary = {}  # force_id -> unit_count
	
	# 遍历所有势力的单位
	for force_id in Game.forces.keys():
		var force = Game.get_force(force_id)
		if not force or force.is_defeated:
			continue
		
		var unit_count = 0
		for unit in force.units:
			if is_instance_valid(unit) and unit.position.distance_to(singularity_position) <= singularity_radius:
				unit_count += 1
		
		if unit_count > 0:
			occupant_forces[force_id] = unit_count
	
	# 找到单位数量最多的势力
	var max_units = 0
	var dominant_force_id = -1
	
	for force_id in occupant_forces.keys():
		if occupant_forces[force_id] > max_units:
			max_units = occupant_forces[force_id]
			dominant_force_id = force_id
	
	# 返回该势力的队伍ID
	if dominant_force_id >= 0:
		var force = Game.get_force(dominant_force_id)
		return force.team_id if force else -1
	
	return -1

func activate_singularity():
	"""激活奇点（弑君模式）"""
	if not Game.map_ref:
		return
	
	# 在地图中心放置奇点
	singularity_position = Vector2(
		Game.map_ref.map_size.x * Config.CELL_SIZE / 2,
		Game.map_ref.map_size.y * Config.CELL_SIZE / 2
	)
	
	singularity_active = true
	print("Singularity activated at ", singularity_position)

func achieve_victory(winner_team_id: int, victory_type: String):
	"""达成胜利"""
	if victory_achieved_flag:
		return
	
	victory_achieved_flag = true
	victory_achieved.emit(winner_team_id, victory_type)
	
	print("Victory achieved! Team ", winner_team_id, " wins by ", victory_type)
	
	# 结束游戏
	Game.end_match(winner_team_id)

func check_victory():
	"""检查胜利条件（由外部调用）"""
	if victory_achieved_flag:
		return
	
	match current_victory_type:
		VictoryType.CONQUEST:
			update_conquest()
		_:
			pass  # 其他模式在 update 中持续检查

# 查询接口
func get_victory_progress(team_id: int) -> float:
	"""获取胜利进度（0-1）"""
	match current_victory_type:
		VictoryType.CONQUEST:
			# 征服模式：计算敌对核心摧毁比例
			return calculate_conquest_progress(team_id)
		VictoryType.DOMINATION:
			var score = domination_scores.get(team_id, 0.0)
			return score / Config.VICTORY_TARGET_SCORE
		VictoryType.KING_OF_THE_HILL:
			var score = koth_scores.get(team_id, 0.0)
			return score / Config.VICTORY_TARGET_SCORE
		_:
			return 0.0

func calculate_conquest_progress(team_id: int) -> float:
	"""计算征服进度"""
	var total_enemy_cores = 0
	var destroyed_enemy_cores = 0
	
	# 统计敌对核心
	for other_team_id in Game.teams.keys():
		if other_team_id == team_id:
			continue
		
		var other_team = Game.get_team(other_team_id)
		if not other_team:
			continue
		
		for force_id in other_team.force_ids:
			total_enemy_cores += 1
			var force = Game.get_force(force_id)
			if not force or force.is_defeated or not force.core_ref or not is_instance_valid(force.core_ref):
				destroyed_enemy_cores += 1
	
	if total_enemy_cores == 0:
		return 1.0
	
	return float(destroyed_enemy_cores) / float(total_enemy_cores)

func get_victory_info() -> Dictionary:
	"""获取胜利信息"""
	var info = {
		"type": get_victory_type_name(),
		"progress": {},
		"active": not victory_achieved_flag
	}
	
	for team_id in Game.teams.keys():
		info.progress[team_id] = get_victory_progress(team_id)
	
	if current_victory_type == VictoryType.KING_OF_THE_HILL:
		info["singularity_active"] = singularity_active
		info["singularity_position"] = singularity_position
		info["singularity_occupant"] = get_singularity_occupant()
	
	return info

func get_victory_type_name() -> String:
	"""获取胜利类型名称"""
	match current_victory_type:
		VictoryType.CONQUEST:
			return "Conquest"
		VictoryType.DOMINATION:
			return "Domination"
		VictoryType.KING_OF_THE_HILL:
			return "King of the Hill"
		_:
			return "Unknown"
