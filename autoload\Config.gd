extends Node

# 游戏配置常量
# 所有可调参数集中在此处管理

# 系统配置
const LOGIC_TICK_RATE: float = 20.0  # 逻辑更新频率 Hz
const MAX_UNITS_PER_FORCE: int = 200
const MAX_TOTAL_UNITS: int = 600

# 地图配置
const DEFAULT_MAP_SIZE: Vector2i = Vector2i(100, 100)
const MIN_MAP_SIZE: int = 50
const MAX_MAP_SIZE: int = 2000
const CELL_SIZE: int = 32
const ENERGY_TILE_RATIO: float = 0.01  # 能源格占地图比例 (1%)

# 核心配置
const CORE_BASE_HP: int = 1000
const CORE_BASE_DEFENSE: int = 50
const CORE_INCOME_PER_SEC_PER_LEVEL: int = 30
const CORE_UPGRADE_COST_FACTOR: int = 1000
const CORE_MAX_LEVEL: int = 10
const CORE_EXPLOSION_RADIUS_CELLS: int = 10
const CORE_EXPLOSION_DAMAGE_CENTER: int = 200

# 经济配置
const INITIAL_ENERGY: int = 1000
const INITIAL_ENERGY_MIN: int = 0
const INITIAL_ENERGY_MAX: int = 200000
const ENERGY_TILE_INCOME_PER_SEC: int = 1
const BOUNTY_FACTOR: float = 0.1  # 赏金 = 单位价格 * 因子

# 单位属性范围
const UNIT_HP_MIN: float = 0.0
const UNIT_HP_MAX: float = 1000.0
const UNIT_ATTACK_MIN: float = 0.0
const UNIT_ATTACK_MAX: float = 1000.0
const UNIT_DEFENSE_MIN: float = 0.0
const UNIT_DEFENSE_MAX: float = 1000.0
const UNIT_ATTACK_SPEED_MIN: float = 0.017
const UNIT_ATTACK_SPEED_MAX: float = 1000.0
const UNIT_ATTACK_RANGE_MIN: float = 0.0
const UNIT_ATTACK_RANGE_MAX: float = 50.0
const UNIT_SPEED_MIN: float = 0.0
const UNIT_SPEED_MAX: float = 60.0

# 单位升级配置
const UNIT_MAX_LEVEL: int = 3
const UNIT_UPGRADE_KILLS_REQUIRED: int = 3
const UNIT_UPGRADE_SURVIVAL_TIME: float = 180.0  # 3分钟
const UNIT_LEVEL_HP_GROWTH: float = 0.2  # 每级+20%
const UNIT_LEVEL_ATTACK_GROWTH: float = 0.2
const UNIT_LEVEL_DEFENSE_GROWTH: float = 0.2
const UNIT_LEVEL_SPEED_GROWTH: float = 0.1  # 每级+10%
const UNIT_LEVEL_RANGE_GROWTH: float = 0.1
const UNIT_LEVEL_SIZE_GROWTH: float = 0.2

# 成本计算配置
const COST_HP_FACTOR: float = 1.0
const COST_ATTACK_FACTOR: float = 1.0
const COST_DEFENSE_FACTOR: float = 1.0
const COST_SPEED_FACTOR: float = 5.0
const COST_RANGE_FACTOR: float = 0.2  # 每5格攻击距离计1能量
const BUILD_TIME_FACTOR: float = 0.05  # 每10能量价格对应0.5秒

# 胜利条件配置
const VICTORY_TARGET_SCORE: int = 100
const DOMINATION_THRESHOLD: float = 0.5  # 需要占领50%以上能源格
const DOMINATION_GAIN_FACTOR: float = 1.0  # 胜利点增长速度 = 占比 * 因子
const VICTORY_DECAY_RATE: float = 1.0  # 胜利点衰减速度 点/秒
const KOTH_GAIN_RATE: float = 0.5  # 弑君模式胜利点增长速度 点/秒

# 事件配置
const EVENT_NEST_TIME: float = 300.0  # 5分钟
const EVENT_FINAL_TIME: float = 720.0  # 12分钟
const NEST_REWARD_ENERGY: int = 500  # 摧毁巢穴奖励
const BOSS_BUFF_ATTACK_BONUS: float = 0.15  # Boss击杀奖励：全体攻击力+15%
const BOSS_BUFF_DURATION: float = 180.0  # Buff持续3分钟

# 势力颜色配置
const FORCE_COLORS: Array[Color] = [
	Color(0.8, 0.1, 0.1),  # 深红
	Color(0.1, 0.3, 0.8),  # 宝蓝
	Color(0.1, 0.6, 0.2),  # 森绿
	Color(0.9, 0.7, 0.1),  # 金黄
	Color(0.7, 0.2, 0.8),  # 亮紫
	Color(1.0, 0.5, 0.1),  # 亮橙
	Color(0.1, 0.8, 0.8),  # 青色
	Color(0.9, 0.2, 0.6),  # 品红
]

const NEUTRAL_COLOR: Color = Color(0.5, 0.5, 0.5)  # 原初体灰色

# AI 配置
const AI_PRODUCTION_INTERVAL: float = 5.0  # AI生产间隔
const AI_RETARGET_INTERVAL: float = 1.0   # AI重新选择目标间隔
const AI_PATHFIND_INTERVAL: float = 0.5   # 单位重新寻路间隔

# 性能配置
const VISIBILITY_CULL_DISTANCE: float = 1000.0  # 可见性剔除距离
const PATHFIND_CACHE_SIZE: int = 1000  # 路径缓存大小

# 调试配置
const DEBUG_DRAW_PATHS: bool = false
const DEBUG_DRAW_ATTACK_RANGES: bool = false
const DEBUG_SHOW_UNIT_INFO: bool = false

# 工具函数
static func clamp_unit_stat(value: float, stat_name: String) -> float:
	"""限制单位属性在有效范围内"""
	match stat_name:
		"hp":
			return clamp(value, UNIT_HP_MIN, UNIT_HP_MAX)
		"attack":
			return clamp(value, UNIT_ATTACK_MIN, UNIT_ATTACK_MAX)
		"defense":
			return clamp(value, UNIT_DEFENSE_MIN, UNIT_DEFENSE_MAX)
		"attack_speed":
			return clamp(value, UNIT_ATTACK_SPEED_MIN, UNIT_ATTACK_SPEED_MAX)
		"attack_range":
			return clamp(value, UNIT_ATTACK_RANGE_MIN, UNIT_ATTACK_RANGE_MAX)
		"speed":
			return clamp(value, UNIT_SPEED_MIN, UNIT_SPEED_MAX)
		_:
			return value

static func calculate_unit_price(stats: Dictionary) -> int:
	"""计算单位价格"""
	var price = 0
	price += int(stats.get("hp", 0) * COST_HP_FACTOR)
	price += int(stats.get("attack", 0) * COST_ATTACK_FACTOR)
	price += int(stats.get("defense", 0) * COST_DEFENSE_FACTOR)
	price += int(stats.get("speed", 0) * COST_SPEED_FACTOR)
	price += int(stats.get("attack_range", 0) * COST_RANGE_FACTOR)
	return max(1, price)

static func calculate_build_time(price: int) -> float:
	"""计算建造时间"""
	return price * BUILD_TIME_FACTOR

static func get_force_color(index: int) -> Color:
	"""获取势力颜色"""
	if index >= 0 and index < FORCE_COLORS.size():
		return FORCE_COLORS[index]
	return FORCE_COLORS[0]
