extends Node

# 全局游戏状态管理器
# 负责游戏流程、势力管理、胜利条件、事件触发

signal game_started
signal game_ended(winner_team_id: int)
signal force_defeated(force_id: int)

# 游戏状态
enum GameState {
	SETUP,
	PLAYING,
	ENDED
}

var current_state: GameState = GameState.SETUP
var game_time: float = 0.0
var tick_timer: Timer

# 势力和队伍
var forces: Dictionary = {}  # force_id -> Force
var teams: Dictionary = {}   # team_id -> Team
var next_force_id: int = 1
var next_team_id: int = 1

# 系统引用
var map_ref: Node = null
var economy_ref: Node = null
var production_ref: Node = null
var combat_ref: Node = null
var victory_ref: Node = null
var events_ref: Node = null
var hud_ref: Node = null

# 游戏设置
var map_size: Vector2i = Vector2i(100, 100)
var initial_energy: int = 1000
var victory_mode: String = "conquest"  # conquest, domination, koth

func _ready():
	# 创建逻辑 tick 定时器
	tick_timer = Timer.new()
	tick_timer.wait_time = 1.0 / Config.LOGIC_TICK_RATE
	tick_timer.timeout.connect(_on_logic_tick)
	add_child(tick_timer)
	
	print("Game manager initialized")

func start_match(settings: Dictionary = {}):
	"""开始游戏匹配"""
	if current_state != GameState.SETUP:
		return
	
	# 应用设置
	if settings.has("map_size"):
		map_size = settings.map_size
	if settings.has("initial_energy"):
		initial_energy = settings.initial_energy
	if settings.has("victory_mode"):
		victory_mode = settings.victory_mode
	
	current_state = GameState.PLAYING
	game_time = 0.0
	
	# 启动逻辑 tick
	tick_timer.start()
	
	print("Game started with settings: ", settings)
	game_started.emit()

func register_force(force_data: Dictionary) -> int:
	"""注册新势力"""
	var force_id = next_force_id
	next_force_id += 1
	
	var force = Force.new()
	force.id = force_id
	force.color = force_data.get("color", Color.BLUE)
	force.team_id = force_data.get("team_id", register_team())
	force.energy = initial_energy
	force.is_ai = force_data.get("is_ai", false)
	
	forces[force_id] = force
	
	# 将势力添加到队伍
	if not teams.has(force.team_id):
		teams[force.team_id] = Team.new()
		teams[force.team_id].id = force.team_id
	
	teams[force.team_id].force_ids.append(force_id)
	
	print("Registered force ", force_id, " with color ", force.color, " in team ", force.team_id)
	return force_id

func register_team() -> int:
	"""注册新队伍"""
	var team_id = next_team_id
	next_team_id += 1
	return team_id

func get_force(force_id: int) -> Force:
	"""获取势力"""
	return forces.get(force_id)

func get_team(team_id: int) -> Team:
	"""获取队伍"""
	return teams.get(team_id)

func get_enemy_forces(force_id: int) -> Array[int]:
	"""获取敌对势力列表"""
	var my_force = get_force(force_id)
	if not my_force:
		return []
	
	var enemies: Array[int] = []
	for other_id in forces.keys():
		var other_force = forces[other_id]
		if other_force.team_id != my_force.team_id:
			enemies.append(other_id)
	
	return enemies

func defeat_force(force_id: int):
	"""击败势力"""
	var force = get_force(force_id)
	if not force or force.is_defeated:
		return
	
	force.is_defeated = true
	print("Force ", force_id, " has been defeated!")
	force_defeated.emit(force_id)
	
	# 检查胜利条件
	_check_victory()

func end_match(winner_team_id: int):
	"""结束游戏"""
	if current_state != GameState.PLAYING:
		return
	
	current_state = GameState.ENDED
	tick_timer.stop()
	
	print("Game ended! Winner team: ", winner_team_id)
	game_ended.emit(winner_team_id)

func _on_logic_tick():
	"""逻辑 tick 处理"""
	if current_state != GameState.PLAYING:
		return
	
	var delta = tick_timer.wait_time
	game_time += delta
	
	# 更新各个系统
	if economy_ref:
		economy_ref.update(delta)
	if victory_ref:
		victory_ref.update(delta)
	if events_ref:
		events_ref.update(game_time)
	
	# 更新 HUD
	if hud_ref:
		hud_ref.update_display()

func _check_victory():
	"""检查胜利条件"""
	if victory_ref:
		victory_ref.check_victory()

# 内部类定义
class Force:
	var id: int
	var color: Color
	var team_id: int
	var energy: int = 0
	var is_ai: bool = false
	var is_defeated: bool = false
	var core_ref: Node = null
	var units: Array[Node] = []
	
	func add_unit(unit: Node):
		units.append(unit)
	
	func remove_unit(unit: Node):
		units.erase(unit)

class Team:
	var id: int
	var force_ids: Array[int] = []
	var victory_score: float = 0.0
