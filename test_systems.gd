extends SceneTree

# 系统测试脚本
# 用于验证各个系统的基本功能

func _init():
	print("=== Geometric Warfare 2 - System Test ===")
	
	# 测试配置系统
	test_config_system()
	
	# 测试蓝图系统
	test_blueprint_system()
	
	# 测试数学工具
	test_math_utils()
	
	print("=== All tests completed ===")
	quit()

func test_config_system():
	print("\n--- Testing Config System ---")
	
	# 测试常量
	print("CELL_SIZE: ", Config.CELL_SIZE)
	print("MAX_UNITS_PER_FORCE: ", Config.MAX_UNITS_PER_FORCE)
	print("CORE_BASE_HP: ", Config.CORE_BASE_HP)
	
	# 测试颜色
	print("Force colors count: ", Config.FORCE_COLORS.size())
	print("First color: ", Config.get_force_color(0))
	
	# 测试属性限制
	var clamped_hp = Config.clamp_unit_stat(1500.0, "hp")
	print("Clamped HP (1500 -> ", clamped_hp, ")")
	
	# 测试价格计算
	var test_stats = {
		"hp": 100.0,
		"attack": 50.0,
		"defense": 10.0,
		"speed": 20.0,
		"attack_range": 5.0
	}
	var price = Config.calculate_unit_price(test_stats)
	var build_time = Config.calculate_build_time(price)
	print("Test unit price: ", price, ", build time: ", build_time)

func test_blueprint_system():
	print("\n--- Testing Blueprint System ---")
	
	# 创建默认蓝图
	var circle_bp = Blueprint.create_default_circle()
	print("Circle blueprint: ", circle_bp.unit_name)
	print("  HP: ", circle_bp.get_stat("hp"))
	print("  Attack: ", circle_bp.get_stat("attack"))
	print("  Price: ", circle_bp.get_price())
	print("  Build time: ", circle_bp.get_build_time())
	
	var triangle_bp = Blueprint.create_default_triangle()
	print("Triangle blueprint: ", triangle_bp.unit_name)
	print("  Speed: ", triangle_bp.get_stat("speed"))
	print("  Price: ", triangle_bp.get_price())
	
	# 测试蓝图修改
	var custom_bp = circle_bp.duplicate_blueprint()
	custom_bp.set_stat("hp", 200.0)
	print("Custom blueprint HP: ", custom_bp.get_stat("hp"))
	print("Custom blueprint price: ", custom_bp.get_price())

func test_math_utils():
	print("\n--- Testing Math Utils ---")
	
	# 测试形状生成
	var triangle_points = Math2D.generate_shape_points(Blueprint.Shape.TRIANGLE, 20.0)
	print("Triangle points count: ", triangle_points.size())
	if triangle_points.size() > 0:
		print("First point: ", triangle_points[0])
	
	var circle_points = Math2D.generate_shape_points(Blueprint.Shape.CIRCLE, 15.0)
	print("Circle points count: ", circle_points.size())
	
	# 测试距离计算
	var p1 = Vector2(0, 0)
	var p2 = Vector2(3, 4)
	var distance = Math2D.distance(p1, p2)
	print("Distance (0,0) to (3,4): ", distance)
	
	# 测试边界计算
	var bounds = Math2D.get_shape_bounds(triangle_points)
	print("Triangle bounds: ", bounds)
