extends Node
class_name ProductionSystem

# 生产系统
# 管理单位生产、队列、成本计算等

signal production_started(force_id: int, blueprint: Blueprint)
signal production_completed(force_id: int, unit: Node2D)
signal production_failed(force_id: int, blueprint: Blueprint, reason: String)

# 默认蓝图库
var default_blueprints: Dictionary = {}

func _ready():
	# 设置系统引用
	Game.production_ref = self
	
	# 创建默认蓝图
	create_default_blueprints()
	
	print("Production system initialized")

func create_default_blueprints():
	"""创建默认蓝图库"""
	default_blueprints[Blueprint.Shape.CIRCLE] = Blueprint.create_default_circle()
	default_blueprints[Blueprint.Shape.TRIANGLE] = Blueprint.create_default_triangle()
	default_blueprints[Blueprint.Shape.SQUARE] = Blueprint.create_default_square()
	default_blueprints[Blueprint.Shape.PENTAGON] = Blueprint.create_default_pentagon()
	default_blueprints[Blueprint.Shape.HEXAGON] = Blueprint.create_default_hexagon()
	
	print("Created ", default_blueprints.size(), " default blueprints")

func get_default_blueprint(shape: Blueprint.Shape) -> Blueprint:
	"""获取默认蓝图"""
	return default_blueprints.get(shape)

func get_all_default_blueprints() -> Array[Blueprint]:
	"""获取所有默认蓝图"""
	var blueprints: Array[Blueprint] = []
	for shape in [Blueprint.Shape.CIRCLE, Blueprint.Shape.TRIANGLE, Blueprint.Shape.SQUARE, 
				  Blueprint.Shape.PENTAGON, Blueprint.Shape.HEXAGON]:
		blueprints.append(default_blueprints[shape])
	return blueprints

func can_produce(force_id: int, blueprint: Blueprint) -> bool:
	"""检查是否可以生产"""
	var force = Game.get_force(force_id)
	if not force:
		return false
	
	# 检查能量
	if force.energy < blueprint.get_price():
		return false
	
	# 检查人口上限（如果需要）
	if force.units.size() >= Config.MAX_UNITS_PER_FORCE:
		return false
	
	# 检查核心是否存在
	if not force.core_ref or not is_instance_valid(force.core_ref):
		return false
	
	return true

func request_production(force_id: int, blueprint: Blueprint) -> bool:
	"""请求生产单位"""
	if not can_produce(force_id, blueprint):
		production_failed.emit(force_id, blueprint, "Cannot produce unit")
		return false
	
	var force = Game.get_force(force_id)
	var price = blueprint.get_price()
	
	# 扣除能量
	if Game.economy_ref and Game.economy_ref.spend_energy(force_id, price):
		# 加入核心生产队列
		if force.core_ref:
			force.core_ref.enqueue_production(blueprint)
			production_started.emit(force_id, blueprint)
			print("Production requested: ", blueprint.unit_name, " for force ", force_id)
			return true
	
	production_failed.emit(force_id, blueprint, "Insufficient energy")
	return false

func get_production_info(force_id: int) -> Dictionary:
	"""获取生产信息"""
	var force = Game.get_force(force_id)
	if not force or not force.core_ref:
		return {}
	
	var core = force.core_ref
	var info = {
		"queue_size": core.get_production_queue_size(),
		"current_production": null,
		"current_progress": 0.0
	}
	
	if core.current_production:
		info["current_production"] = core.current_production.unit_name
		var total_time = core.current_production.get_build_time()
		var remaining_time = core.production_timer
		info["current_progress"] = 1.0 - (remaining_time / total_time) if total_time > 0 else 1.0
	
	return info

# 蓝图管理功能（为后续扩展预留）
func save_blueprint(blueprint: Blueprint, filename: String) -> bool:
	"""保存蓝图到文件"""
	var path = "user://blueprints/" + filename + ".tres"
	var error = ResourceSaver.save(blueprint, path)
	return error == OK

func load_blueprint(filename: String) -> Blueprint:
	"""从文件加载蓝图"""
	var path = "user://blueprints/" + filename + ".tres"
	if ResourceLoader.exists(path):
		return load(path) as Blueprint
	return null

func get_blueprint_library(force_id: int) -> Dictionary:
	"""获取势力的蓝图库（目前返回默认蓝图）"""
	# 未来可以扩展为每个势力自定义蓝图库
	return default_blueprints.duplicate()

func validate_blueprint(blueprint: Blueprint) -> bool:
	"""验证蓝图是否有效"""
	if not blueprint:
		return false
	
	# 检查名称
	if blueprint.unit_name.is_empty() or blueprint.unit_name.length() > 30:
		return false
	
	# 检查属性范围
	for stat_name in blueprint.base_stats.keys():
		var value = blueprint.base_stats[stat_name]
		var clamped_value = Config.clamp_unit_stat(value, stat_name)
		if abs(value - clamped_value) > 0.001:  # 浮点数比较
			return false
	
	return true

func calculate_total_production_cost(force_id: int) -> int:
	"""计算当前生产队列的总成本"""
	var force = Game.get_force(force_id)
	if not force or not force.core_ref:
		return 0
	
	var total_cost = 0
	var core = force.core_ref
	
	# 当前生产的单位
	if core.current_production:
		total_cost += core.current_production.get_price()
	
	# 队列中的单位
	for blueprint in core.production_queue:
		total_cost += blueprint.get_price()
	
	return total_cost

func get_estimated_completion_time(force_id: int) -> float:
	"""获取预计完成时间"""
	var force = Game.get_force(force_id)
	if not force or not force.core_ref:
		return 0.0
	
	var total_time = 0.0
	var core = force.core_ref
	
	# 当前生产剩余时间
	if core.current_production:
		total_time += core.production_timer
	
	# 队列中的单位时间
	for blueprint in core.production_queue:
		total_time += blueprint.get_build_time()
	
	return total_time
